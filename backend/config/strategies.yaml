# 决策引擎策略配置文件 v1.0
# 定义了系统在不同状态(state)、不同意图(intent)、不同情感(emotion)下的响应策略。

# 默认策略：当所有查找都失败时使用，是系统的最终安全网。
DEFAULT_STRATEGY:
  action: "handle_unknown_situation" # 指令：处理未知情况
  priority: 0
  prompt_instruction: "保持中性、专业的语气，表示暂时无法理解，并请求用户澄清。"

# 全局策略：适用于所有未被特定状态覆盖的通用场景。
GLOBAL:
  
  greeting:
    neutral:
      action: "respond_with_greeting" # 指令：回复问候
      priority: 1
      prompt_instruction: "用户向你问候，请友好回应并简要介绍你是一个AI需求采集助手，专门帮助用户整理和分析业务需求。然后询问用户有什么需求需要帮助整理。回复要简洁、专业、友好。"
  
  complete:
    positive:
      action: "finalize_and_reset" # 指令：确认完成并重置
      priority: 1
      prompt_instruction: "用户愉快地表示完成了，请表达感谢并愉快地结束当前任务或对话。"

  reset: # 新增一个全局重置意图
    neutral:
      action: "reset_conversation" # 指令：重置会话
      priority: 10
      prompt_instruction: "用户请求重置，这是一个高优先级指令。"

  provide_information:
    negative:
      action: "show_empathy_and_clarify"
      priority: 7
      prompt_instruction: "用户提供了带有负面情绪的信息。请首先表示理解和共情（例如：听到这个消息我很难过），然后询问是否需要帮助，或者引导对话回到核心业务上。"
    neutral:
      action: "acknowledge_and_redirect"
      priority: 2
      prompt_instruction: "用户提供了一条与当前流程无关的信息。请礼貌地确认收到信息，然后温和地将对话引导回我们的主要任务上。"
    positive:
      action: "acknowledge_and_redirect"
      priority: 2
      prompt_instruction: "用户分享了积极的信息。请表达赞同或高兴，然后温和地将对话引导回我们的主要任务上。"

  unknown:
    neutral:
      action: "request_clarification" # 指令：请求澄清
      priority: 5
      prompt_instruction: "无法理解用户的意图，请礼貌地请求用户用其他方式重新描述一下。"

# 空闲状态：对话开始时的特定策略
IDLE:
  ask_question: # 代表一个新需求的开始
    neutral:
      action: "start_requirement_gathering" # 指令：开始需求收集
      priority: 5
      prompt_instruction: "这是一个全新的需求，用户的需求可能非常开放和模糊。核心目标不是猜测一个具体答案，而是通过一个高质量的引导性问题，帮助用户将想法聚焦到具体可执行的业务领域。"
  provide_information: # 新增的规则，当意图是提供信息时
    neutral:
      action: "start_requirement_gathering" # 执行和上面完全一样的action
      priority: 5
      prompt_instruction: "收到您的需求，正在为您分析..."
  # --- 新增的容错规则 ---
  request_clarification: # 规则3: 即使用户意图被错误识别为“请求澄清”
    neutral:
      # 我们也假定这是一个新需求的开始，执行同样的action
      action: "start_requirement_gathering"
      priority: 5
      prompt_instruction: "收到您的请求，正在为您分析并准备提问..."
# 需求收集状态
# 意图处理中状态
PROCESSING_INTENT:
  provide_information: # 用户在澄清意图时提供了更多信息
    neutral:
      action: "continue_requirement_gathering" # 指令：继续需求收集
      priority: 5
      prompt_instruction: "用户提供了更多信息，请基于新信息继续进行领域和类别的判断。"

# 信息收集中状态
COLLECTING_INFO:
  provide_information:
    neutral:
      action: "process_answer_and_ask_next" # 指令：处理回答并问下一个
      priority: 5
      prompt_instruction: "用户提供了对当前问题的回答。请确认信息，然后引导至下一个问题。"

  ask_question:
    neutral:
      # 当用户回答被错误识别为“提问”时，我们假定他其实是在“提供信息”
      action: "process_answer_and_ask_next" 
      priority: 5
      prompt_instruction: "正在处理您的回答并准备下一个问题..."

  confirm: # <--- 新增的策略
    neutral:
      action: "process_answer_and_ask_next"
      priority: 5
      prompt_instruction: "用户确认了信息或回答了问题，请处理并准备下一个问题。"    
      

  reject: # 用户在信息收集中说“不对”或“不是这样的”
    negative:
      action: "request_clarification"      # 指令：重新提问
      priority: 8
      prompt_instruction: "用户否定了我们刚才的提议或问题。请不要直接道歉，而是重新组织一下你的问题，或者提供几个选项，并询问用户的具体想法是什么。"
    neutral:
      action: "request_clarification"      # 指令：重新提问
      priority: 8
      prompt_instruction: "用户表示无法提供相关信息。请理解用户的情况，然后询问是否可以跳过这个问题，或者提供一些具体的选项来帮助用户回答。"

  request_clarification: # 当用户在信息收集中感到困惑并请求帮助或建议时
    neutral:
      action: "provide_suggestions" # 指令：提供建议
      priority: 8 # 这是一个重要的用户求助信号，优先级设高一些
      prompt_instruction: "用户在回答问题时请求建议。请根据当前对话的上下文，提供2-3个具体的、可操作的建议或示例，帮助用户思考和回答。如果用户表示不需要更多信息，应该继续下一个问题或结束收集。"

  

  skip: # 新增一个跳过问题的意图
    neutral:
      action: "skip_question_and_ask_next" # 指令：跳过问题并问下一个
      priority: 6
      prompt_instruction: "用户希望跳过当前问题。"

  complete:
    neutral:
      # 当用户表示“完成”时，我们也执行处理回答的流程。
      # 该流程会自动检查是否所有问题都已问完，并触发文档生成。
      action: "process_answer_and_ask_next"
      priority: 6
      prompt_instruction: "用户表示信息已提供完毕，准备结束收集阶段。"
    positive:
      # 同时处理积极的完成意图
      action: "process_answer_and_ask_next"
      priority: 6
      prompt_instruction: "用户愉快地表示信息已提供完毕，准备结束收集阶段。"
  # --------------------    

# 文档生成/审查中状态
DOCUMENTING:
  confirm:
    positive:
      action: "finalize_and_reset" # 指令：确认完成并重置
      priority: 6
      prompt_instruction: "用户对最终文档表示了积极的确认。请用热情的语气庆祝项目达成一致，并正式结束本次需求采集流程。"
  
  modify:
    neutral:
      action: "execute_document_modification" # 指令：执行文档修改
      priority: 7
      prompt_instruction: "用户要求修改文档，请先清晰地复述一遍你理解的修改点以进行确认，然后说明将如何执行修改。"

  reject: # 用户在文档审查中说“不对”
    negative:
      action: "apologize_and_request_refinement" # 指令：道歉并请求明确修改意见
      priority: 9
      prompt_instruction: "用户明确否定了文档内容，这是一个严重的问题。请务必先真诚道歉，然后主动承担责任，并询问具体需要修改的地方，引导用户给出明确的修改意见。"