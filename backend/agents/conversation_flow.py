"""
对话流程管理器（AutoGenConversationFlowAgent） - v2.10 (采用数据驱动的建议生成)
"""

# 导入必要的模块和库
from enum import Enum, auto
from typing import Any, Dict, List, Optional, Union
import logging
import json # 确保导入json模块
import sys
import os
import asyncio
from collections import defaultdict
from datetime import datetime

# 添加项目根目录到系统路径，便于模块导入
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

# 导入项目内部模块
from .base import AutoGenBaseAgent
from .conversation_flow_reply_mixin import ConversationFlowReplyMixin
from .conversation_flow_message_mixin import ConversationFlowMessageMixin
from backend.utils.logging_config import get_logger
from backend.utils.prompt_loader import PromptLoader
from backend.data.db.database_manager import DatabaseManager
from backend.data.db.focus_point_manager import FocusPointManager
from backend.data.db.message_manager import MessageManager
from backend.data.db.summary_manager import SummaryManager
from backend.config import settings


# 定义对话状态枚举类
class ConversationState(Enum):
    """对话状态枚举"""
    IDLE = auto()              # 1 空闲状态
    PROCESSING_INTENT = auto() # 2 处理意图
    COLLECTING_INFO = auto()   # 3 收集需求信息
    DOCUMENTING = auto()       # 4 文档生成与修改

# 导入其他Agent模块
from backend.agents.domain_classifier import AutoGenDomainClassifierAgent

from backend.agents.category_classifier import CategoryClassifierAgent


# 管理关注点状态
class FocusPointStatusManager:
    def __init__(self, db_manager: DatabaseManager):
        self.db_manager = db_manager
        self.focus_points_status = defaultdict(dict)
        self.logger = logging.getLogger(__name__)
        self.current_session_id = None  # 当前会话ID

    # 初始化关注点状态
    async def initialize_focus_points(self, session_id: str, focus_points: List[Dict[str, Any]]):
        try:
            # 保存当前会话ID
            self.current_session_id = session_id

            # 确保会话存在
            await self._ensure_conversation_exists(session_id)

            # 准备批量插入数据
            params_list = []
            for point in focus_points:
                point_id = point["id"]
                # 检查关注点是否已存在
                exists = await self.db_manager.record_exists(
                    "SELECT 1 FROM concern_point_coverage WHERE conversation_id = ? AND focus_id = ?",
                    (session_id, point_id)
                )

                if not exists:
                    params_list.append((
                        session_id,
                        point_id,
                        "pending",  # 初始状态
                        0,          # 尝试次数
                        0,          # 是否已覆盖
                        None,       # 提取的信息 (should be NULL in DB if no value)
                        datetime.now().isoformat()  # 更新时间
                    ))

            # 如果有需要插入的数据
            if params_list:
                await self.db_manager.execute_batch(
                    """
                    INSERT INTO concern_point_coverage
                    (conversation_id, focus_id, status, attempts, is_covered, extracted_info, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                    """,
                    params_list
                )

                self.logger.debug(f"初始化关注点状态完成 - conversation_id: {session_id}, 关注点数量: {len(params_list)}")

            # 清空内存中的状态
            self.focus_points_status = defaultdict(dict)

            # 加载关注点状态到内存
            await self._load_status_to_memory(session_id)

            # 打印内存中的状态
            self.logger.debug(f"内存中的关注点状态: {json.dumps(self.focus_points_status, ensure_ascii=False)}")
        except Exception as e:
            self.logger.error(f"初始化关注点状态失败: {str(e)}")

    # 更新关注点状态
    async def update_focus_point_status(self, session_id: str, point_id: str, status: str, value: str = None,
                                     intent_confidence: float = None, additional_data: Dict[str, Any] = None) -> bool:
        try:
            # 确保会话存在
            await self._ensure_conversation_exists(session_id)

            # 检查记录是否存在
            exists = await self.db_manager.record_exists(
                "SELECT 1 FROM concern_point_coverage WHERE conversation_id = ? AND focus_id = ?",
                (session_id, point_id)
            )

            self.logger.debug(f"更新关注点状态 - 记录是否存在: {exists}, session_id: {session_id}, point_id: {point_id}, status: {status}")

            if exists:
                # 更新现有记录
                query_params = [status]

                # 构建查询
                query = "UPDATE concern_point_coverage SET status = ?"

                if value is not None:
                    query += ", extracted_info = ?"
                    query_params.append(value)

                # 更新尝试次数
                if status == "processing":
                    query += ", attempts = attempts + 1"

                # 更新是否已覆盖
                if status == "completed":
                    query += ", is_covered = 1"

                # 更新时间
                query += ", updated_at = ?"
                query_params.append(datetime.now().isoformat())

                # 添加条件
                query += " WHERE conversation_id = ? AND focus_id = ?"
                query_params.extend([session_id, point_id])

                # 执行更新
                self.logger.debug(f"执行SQL更新: {query}, 参数: {query_params}")
                result = await self.db_manager.execute_update(query, tuple(query_params))
                self.logger.debug(f"SQL更新结果: {result}")
            else:
                # 插入新记录
                insert_query = """
                INSERT INTO concern_point_coverage
                (conversation_id, focus_id, status, attempts, is_covered, extracted_info, updated_at)
                VALUES (?, ?, ?, ?, ?, ?, ?)
                """
                insert_params = (
                    session_id,
                    point_id,
                    status,
                    1 if status == "processing" else 0,
                    1 if status == "completed" else 0,
                    value, # Pass value directly; if it's None, it should become NULL in DB
                    datetime.now().isoformat()
                )
                self.logger.debug(f"执行SQL插入: {insert_query}, 参数: {insert_params}")
                result = await self.db_manager.execute_update(insert_query, insert_params)
                self.logger.debug(f"SQL插入结果: {result}")

            # 更新内存中的状态
            if point_id not in self.focus_points_status:
                self.focus_points_status[point_id] = {}

            self.focus_points_status[point_id]["status"] = status
            if value is not None:
                self.focus_points_status[point_id]["value"] = value
            if intent_confidence is not None:
                self.focus_points_status[point_id]["intent_confidence"] = intent_confidence
            if additional_data:
                self.focus_points_status[point_id].update(additional_data)

            self.logger.debug(f"内存中的关注点状态已更新 - point_id: {point_id}, status: {status}, value: {value}")

            # 验证更新是否成功
            updated_status_row = await self.db_manager.get_record(
                "SELECT status FROM concern_point_coverage WHERE conversation_id = ? AND focus_id = ?",
                (session_id, point_id)
            )
            updated_status = updated_status_row['status'] if updated_status_row else None
            self.logger.debug(f"验证更新结果 - 期望状态: {status}, 实际状态: {updated_status}")

            return True
        except Exception as e:
            self.logger.error(f"更新关注点状态失败: {str(e)}")
            return False

    # 获取关注点状态
    def get_focus_point_status(self, point_id: str) -> Dict[str, Any]:
        return self.focus_points_status.get(point_id, {})

    # 获取所有关注点状态
    def get_all_focus_points_status(self) -> Dict[str, Dict[str, Any]]:
        return self.focus_points_status

    # 重置所有关注点状态为pending
    async def reset(self, session_id: str):
        try:
            # execute_query 通常用于SELECT，对于UPDATE/INSERT/DELETE，应该使用 execute_update
            await self.db_manager.execute_update(
                "UPDATE concern_point_coverage SET status = 'pending' WHERE conversation_id = ?",
                (session_id,)
            )
            self.focus_points_status = defaultdict(dict)
            self.logger.debug(f"重置关注点状态 - conversation_id: {session_id}")
        except Exception as e:
            self.logger.error(f"重置关注点状态失败: {str(e)}")

    # 获取当前正在处理的关注点ID
    async def get_processing_point(self) -> Optional[str]:
        # 如果内存中没有状态，尝试从数据库加载
        if not self.focus_points_status and self.current_session_id:
            await self._load_status_to_memory(self.current_session_id)

        # 从内存中查找正在处理的关注点
        for point_id, status in self.focus_points_status.items():
            if status.get("status") == "processing":
                self.logger.debug(f"找到正在处理的关注点: {point_id}")
                return point_id

        # 如果内存中没有找到，尝试从数据库直接查询
        if self.current_session_id:
            try:
                result = await self.db_manager.get_record(
                    """
                    SELECT focus_id FROM concern_point_coverage
                    WHERE conversation_id = ? AND status = 'processing'
                    LIMIT 1
                    """,
                    (self.current_session_id,)
                )
                if result:
                    point_id = result["focus_id"]
                    self.logger.debug(f"从数据库找到正在处理的关注点: {point_id}")
                    # 更新内存中的状态
                    if point_id not in self.focus_points_status:
                        self.focus_points_status[point_id] = {}
                    self.focus_points_status[point_id]["status"] = "processing"
                    return point_id
            except Exception as e:
                self.logger.error(f"从数据库查询正在处理的关注点失败: {str(e)}")

        self.logger.debug("没有找到正在处理的关注点")
        return None

    # 新增：清理所有processing状态的关注点
    async def clear_all_processing_status(self) -> None:
        """清理所有处于processing状态的关注点，将其重置为pending状态"""
        if not self.current_session_id:
            return

        try:
            # 从数据库查找所有processing状态的关注点
            results = await self.db_manager.execute_query(
                """
                SELECT focus_id FROM concern_point_coverage
                WHERE conversation_id = ? AND status = 'processing'
                """,
                (self.current_session_id,)
            )

            if results:
                self.logger.info(f"发现 {len(results)} 个处于processing状态的关注点，将其重置为pending")
                for row in results:
                    point_id = row["focus_id"]
                    await self.update_focus_point_status(
                        session_id=self.current_session_id,
                        point_id=point_id,
                        status="pending"
                    )
                    self.logger.debug(f"已将关注点 {point_id} 从processing重置为pending")
        except Exception as e:
            self.logger.error(f"清理processing状态失败: {str(e)}")

    # 新增：安全地设置关注点为processing状态
    async def set_point_processing(self, session_id: str, point_id: str) -> bool:
        """安全地设置关注点为processing状态，确保只有一个关注点处于processing状态"""
        try:
            # 确保current_session_id已设置
            if not self.current_session_id:
                self.current_session_id = session_id

            # 首先清理所有其他的processing状态
            await self.clear_all_processing_status()

            # 然后设置当前关注点为processing
            success = await self.update_focus_point_status(
                session_id=session_id,
                point_id=point_id,
                status="processing"
            )

            if success:
                self.logger.info(f"已安全设置关注点 {point_id} 为processing状态")
            else:
                self.logger.error(f"设置关注点 {point_id} 为processing状态失败")

            return success
        except Exception as e:
            self.logger.error(f"安全设置processing状态失败: {str(e)}")
            return False

    # 获取下一个待处理的关注点
    async def get_next_pending_point(self, focus_points: List[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
        # 如果内存中没有状态，尝试从数据库加载
        if not self.focus_points_status and self.current_session_id:
            await self._load_status_to_memory(self.current_session_id)

        self.logger.debug(f"获取下一个待处理的关注点 - 关注点数量: {len(focus_points)}")
        self.logger.debug(f"内存中的关注点状态: {json.dumps(self.focus_points_status, ensure_ascii=False)}")

        # 优先返回P0/P1级别的未处理关注点
        for point in sorted(focus_points, key=lambda x: x["priority"]):
            point_id = point["id"]
            status = self.focus_points_status.get(point_id, {}).get("status", "pending")
            self.logger.debug(f"检查关注点 {point_id} - 状态: {status}, 优先级: {point['priority']}")
            if status == "pending" and point["priority"] in ["P0", "P1"]:
                self.logger.debug(f"找到下一个待处理的关注点(P0/P1): {point_id}")
                return point

        # 其次返回P2级别的未处理关注点
        for point in focus_points:
            point_id = point["id"]
            status = self.focus_points_status.get(point_id, {}).get("status", "pending")
            if status == "pending":
                self.logger.debug(f"找到下一个待处理的关注点(P2): {point_id}")
                return point

        # 如果内存中没有找到，尝试从数据库直接查询
        if self.current_session_id:
            try:
                # 获取所有已存在的关注点状态
                results = await self.db_manager.execute_query(
                    """
                    SELECT focus_id, status FROM concern_point_coverage
                    WHERE conversation_id = ?
                    """,
                    (self.current_session_id,)
                )

                # 构建状态字典
                status_dict = {row["focus_id"]: row["status"] for row in results}

                # 查找未处理的关注点
                for point in sorted(focus_points, key=lambda x: x["priority"]):
                    point_id = point["id"]
                    status = status_dict.get(point_id, "pending")
                    if status == "pending":
                        self.logger.debug(f"从数据库找到下一个待处理的关注点: {point_id}")
                        return point
            except Exception as e:
                self.logger.error(f"从数据库查询待处理的关注点失败: {str(e)}")

        self.logger.debug("没有找到待处理的关注点")
        return None

    # 确保会话在数据库中存在，如果不存在则创建
    async def _ensure_conversation_exists(self, conversation_id: str) -> bool:
        
        try:
            # 检查会话行是否存在
            conversation_exists = await self.db_manager.record_exists(
                "SELECT 1 FROM conversations WHERE conversation_id = ?",
                (conversation_id,)
            )

            if not conversation_exists:
                # 创建新会话
                now = datetime.now().isoformat()
                await self.db_manager.execute_update(
                    """
                    INSERT INTO conversations
                    (conversation_id, user_id, status, created_at, updated_at, last_activity_at)
                    VALUES (?, ?, ?, ?, ?, ?)
                    """,
                    (conversation_id, "default_user", "active", now, now, now)
                )
                self.logger.info(f"创建新会话: {conversation_id}")
            return True
        except Exception as e:
            self.logger.error(f"确保会话存在时失败: {str(e)}")
            return False

    # 加载关注点状态到内存
    async def _load_status_to_memory(self, conversation_id: str):
        try:
            # 保存当前会话ID
            self.current_session_id = conversation_id

            # 清空内存中的状态
            self.focus_points_status = defaultdict(dict)

            # 从数据库加载状态
            results = await self.db_manager.execute_query(
                """
                SELECT focus_id, status, attempts, extracted_info, is_covered, updated_at
                FROM concern_point_coverage
                WHERE conversation_id = ?
                """,
                (conversation_id,)
            )

            if results: # 确保 results 不为 None 或空
                for row in results:
                    self.focus_points_status[row["focus_id"]] = {
                        "status": row["status"],
                        "attempts": row["attempts"],
                        "value": row["extracted_info"],
                        "is_covered": bool(row["is_covered"]),
                        "updated_at": row["updated_at"]
                    }

            self.logger.debug(f"加载关注点状态到内存 - conversation_id: {conversation_id}, 状态数量: {len(self.focus_points_status)}")

            # 打印内存中的状态
            status_summary = {}
            for point_id, status in self.focus_points_status.items():
                status_summary[point_id] = status.get("status", "unknown")
            self.logger.debug(f"关注点状态摘要: {json.dumps(status_summary, ensure_ascii=False)}")

            # 检查是否有正在处理的关注点
            processing_points = [point_id for point_id, status in self.focus_points_status.items() if status.get("status") == "processing"]
            if processing_points:
                self.logger.debug(f"发现正在处理的关注点: {processing_points}")
            else:
                self.logger.debug("没有正在处理的关注点")

            return self.focus_points_status
        except Exception as e:
            self.logger.error(f"加载关注点状态到内存失败: {str(e)}")
            self.focus_points_status = defaultdict(dict)
            return self.focus_points_status

# 定义对话流程管理Agent类
class AutoGenConversationFlowAgent(AutoGenBaseAgent, ConversationFlowReplyMixin, ConversationFlowMessageMixin):
    """
    AutoGen对话流程管理Agent

    这是系统的核心对话管理类，负责处理用户消息、管理对话状态、
    协调各种Agent组件，并生成合适的回复。

    核心功能：
    - 对话状态管理：跟踪和管理对话的各个阶段（IDLE、GATHERING、DOCUMENTING等）
    - 消息处理：接收用户消息，进行意图识别和决策，生成回复
    - 组件协调：协调意图识别、决策引擎、文档生成等各个组件
    - 数据管理：管理会话数据、消息历史、关注点等信息
    - 错误处理：提供完善的错误处理和回退机制

    架构特点：
    - 混入类设计：通过ConversationFlowReplyMixin和ConversationFlowMessageMixin
      实现功能模块化，提高代码可维护性
    - 状态机模式：使用ConversationState枚举管理对话状态转换
    - 异步处理：全面支持异步操作，提高系统性能
    - 组件化：各功能模块独立，支持热插拔和独立测试

    主要组件：
    - IntentDecisionEngine: 意图识别和决策引擎
    - MessageReplyManager: 统一回复管理器
    - DynamicReplyGenerator: 动态回复生成器
    - ReplyMonitoringSystem: 回复监控和分析系统
    - TemplateVersionManager: 模板版本管理器
    - 各种数据管理器：MessageManager、FocusPointManager等

    状态管理：
    - IDLE: 空闲状态，等待用户输入
    - GATHERING: 需求收集状态，正在收集用户需求
    - DOCUMENTING: 文档确认状态，等待用户确认生成的文档

    使用示例：
    ```python
    # 初始化Agent
    agent = AutoGenConversationFlowAgent(llm_client=llm_client)
    await agent.initialize_async_components()

    # 处理用户消息
    response = await agent.process_message({
        "text": "我想开发一个网站",
        "session_id": "user_123"
    })
    ```

    扩展性：
    - 支持添加新的对话状态
    - 支持扩展新的回复类型
    - 支持集成新的AI模型和服务
    - 支持自定义业务逻辑

    监控和分析：
    - 实时监控回复质量和性能
    - 记录用户满意度和系统指标
    - 支持A/B测试和版本管理
    - 提供详细的分析报告和改进建议
    """
    def __init__(self,
                 llm_client=None,                # 大语言模型客户端，用于处理和生成文本
                information_extractor_agent=None,  # 信息提取代理，用于从文本中提取关键信息
                document_generator_agent=None,  # 文档生成代理，用于生成相关的文档或回复
                review_and_refine_agent=None,  # 审查和改进代理，用于审查和优化生成的文本
                domain_classifier_agent=None,  # 领域分类代理，用于识别文本所属的领域
                knowledge_base_agent=None,      # 知识库代理，用于提供背景知识和信息
                category_classifier_agent=None,  # 类别分类代理，用于对文本进行分类
                db_path=None,                  # 数据库路径，用于存储和检索数据
                system_message=None,          # 系统消息，用于记录系统相关的信息
                is_termination_msg=None,        # 终止消息，用于标记对话的终止
                max_consecutive_auto_reply=None, # 最大连续自动回复次数，用于控制自动回复的频率
                human_input_mode=None,        # 人类输入模式，用于指定是否期望人类输入
                code_execution_config=None,    # 代码执行配置，用于配置代码执行的环境和参数
                llm_config=None,               # 大语言模型配置，用于配置大语言模型的行为
                default_auto_reply=None,      # 默认自动回复，用于在没有明确意图时提供的自动回复
                intent_decision_engine=None     # 意图决策引擎，用于根据上下文做出最终的意图决策
                ):

        # 初始化意图决策一体化引擎
        if intent_decision_engine is None:
            from backend.agents.intent_decision_engine import IntentDecisionEngine
            intent_decision_engine = IntentDecisionEngine(llm_client)
        self.intent_decision_engine = intent_decision_engine

        # 构建 AutoGen ConversableAgent 的配置参数字典
        # 这些参数用于初始化父类 ConversableAgent，控制对话行为和LLM交互
        conversable_agent_kwargs = {
            'system_message': system_message,                    # 系统消息，定义Agent的角色和行为
            'is_termination_msg': is_termination_msg,            # 终止消息判断函数，用于确定何时结束对话
            'max_consecutive_auto_reply': max_consecutive_auto_reply,  # 最大连续自动回复次数，防止无限循环
            'human_input_mode': human_input_mode,                # 人类输入模式，控制是否需要人工干预
            'code_execution_config': code_execution_config,      # 代码执行配置，用于执行代码片段
            'llm_config': llm_config,                           # LLM配置，包含模型参数和API设置
            'default_auto_reply': default_auto_reply,           # 默认自动回复内容，当无法生成回复时使用
        }

        # 设置默认名称
        conversable_agent_kwargs['name'] = "AutoGenConversationFlow"

        # 禁用Docker容器执行
        code_execution_config = code_execution_config or {}
        code_execution_config['use_docker'] = False
        conversable_agent_kwargs['code_execution_config'] = code_execution_config

        # 调用父类初始化方法
        super().__init__(**conversable_agent_kwargs)

        # 初始化自身属性
        self.current_state = ConversationState.IDLE  # 当前对话状态，将在process_message中根据实际情况调整
        self.llm_client = llm_client  # LLM客户端实例
        self.llm_config = llm_config  # 保存LLM配置

        # --- 新增属性 ---
        self.problem_statement: Optional[str] = None # 用于记录用户核心问题陈述

        # 初始化数据库管理器
        self.db_path = db_path or str(settings.DATABASE_PATH)
        self.db_manager = DatabaseManager(self.db_path)
        self.focus_point_manager = FocusPointManager(self.db_manager)
        self.message_manager = MessageManager(self.db_manager)
        self.summary_manager = SummaryManager(self.db_manager)

        # 初始化关注点状态管理器
        self.status_manager = FocusPointStatusManager(self.db_manager)
        self.latest_domain_result: Optional[Dict[str, Any]] = None
        self.latest_category_result: Optional[Dict[str, Any]] = None
        self.current_focus_points_definitions: List[Dict[str, Any]] = []

        # 初始化领域分类器
        if domain_classifier_agent is None:
            domain_classifier_agent = AutoGenDomainClassifierAgent(llm_client=llm_client)
        self.domain_classifier_agent = domain_classifier_agent

        # 初始化类别分类器
        self.category_classifier_agent = category_classifier_agent

        # 初始化其他Agent
        self.information_extractor_agent = information_extractor_agent

        # 初始化文档生成器
        if document_generator_agent is None:
            from backend.agents.document_generator import DocumentGenerator
            document_generator_agent = DocumentGenerator(
                llm_client=llm_client,
                db_manager=self.db_manager
            )
        self.document_generator_agent = document_generator_agent

        self.review_and_refine_agent = review_and_refine_agent

        # 初始化知识库代理
        if knowledge_base_agent is None:
            from backend.agents.knowledge_base import KnowledgeBaseAgent
            knowledge_base_agent = KnowledgeBaseAgent(db_path=self.db_path)
        self.knowledge_base_agent = knowledge_base_agent

        # 初始化对话状态管理相关属性
        self.current_domain = None  # 当前领域
        self.current_category = None  # 当前类别
        self.collection_context = {}  # 信息采集上下文
        self.current_document = None  # 当前文档
        self.history = []  # 对话历史
        self.pending_count = 0  # 未完成任务计数
        self.max_pending_attempts = 3  # 最大未完成尝试次数
        
        # 初始化任务路由器字典 - 将action指令映射到对应的处理方法
        self.action_router = {
            # --- 全局指令 ---
            "reset_conversation": self.handle_reset_conversation,  # 重置对话，调用重置会话处理函数
            "respond_with_greeting": self.handle_greeting,  # 回复问候语，调用问候处理函数

            # --- 新增的指令映射 ---
            "show_empathy_and_clarify": self.handle_show_empathy_and_clarify,  # 展示同理心并澄清问题
            "acknowledge_and_redirect": self.handle_acknowledge_and_redirect,  # 确认用户输入并引导话题

            # --- 状态特定指令 ---
            "start_requirement_gathering": self._process_intent,  # 开始需求采集流程
            "continue_requirement_gathering": self._process_intent,  # 继续需求采集流程
            "process_answer_and_ask_next": self.handle_process_answer_and_ask_next,  # 处理用户回答并提出下一个问题
            "skip_question_and_ask_next": self.handle_skip_question_and_ask_next,  # 跳过当前问题并提出下一个问题
            "rephrase_and_inquire": self.handle_rephrase_and_inquire,  # 重新表述问题并继续询问
            "execute_document_modification": self.handle_document_modification,  # 执行文档修改相关操作
            "finalize_and_reset": self.handle_finalize_and_reset,  # 完成流程并重置状态
            "apologize_and_request_refinement": self.handle_apology_and_request,  # 道歉并请求用户补充或细化需求

            # --- 建议提供指令 ---
            "provide_suggestions": self.handle_provide_suggestions,  # 提供建议和指导

            # --- 兜底指令 ---
            "handle_unknown_situation": self.handle_unknown_situation,  # 处理未知或未定义的情况
            "request_clarification": self.handle_request_clarification  # 请求用户进一步澄清
        }

        # 初始化新的回复系统组件
        self._initialize_reply_systems()

    # 回复系统初始化方法已移至 ConversationFlowReplyMixin

    # ==================== 核心消息处理方法 ====================

    async def process_message(self, message_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理用户消息的核心方法

        这是系统的主要入口点，负责处理所有用户消息并生成回复。
        集成了新的回复系统，提供智能决策和监控功能。

        处理流程：
        1. 恢复会话状态，确保Agent实例无状态
        2. 保存用户消息到数据库
        3. 特殊状态处理（如DOCUMENTING状态）
        4. 意图识别和决策分析
        5. 使用整合回复系统生成回复
        6. 回退机制处理失败情况
        7. 记录监控指标和性能数据
        8. 保存AI回复并返回响应

        参数:
            message_data (Dict[str, Any]): 包含用户消息的数据字典
                - text: 用户输入的文本内容
                - session_id: 会话标识符
                - 其他可选字段

        返回:
            Dict[str, Any]: 包含AI回复的响应字典
                - text_response: AI生成的回复文本
                - session_id: 会话标识符
                - 其他状态信息

        异常处理:
            - 捕获所有异常，确保系统稳定性
            - 记录详细错误信息和监控指标
            - 提供用户友好的错误回复

        新特性:
            - 整合决策引擎：智能选择最佳回复策略
            - 监控系统：记录所有回复的性能指标
            - 回退机制：确保在组件失败时仍能正常工作
            - 异步处理：提高系统响应性能
        """
        import time
        start_time = time.time()

        try:
            message = message_data.get("text", "").strip()
            session_id = message_data.get("session_id", "")
            self.logger.debug(f"收到新消息: '{message}', session: {session_id}")

            # 每次都尝试恢复状态，确保Agent实例是无状态的
            await self.try_restore_session_state(session_id)

            await self.message_manager.save_message(
                conversation_id=session_id, sender_type="user", content=message
            )

            action_command = None
            decision_result = {}
            history = await self.message_manager.load_conversation_history(session_id)

            # 特殊状态处理逻辑保持不变
            if self.current_state == ConversationState.DOCUMENTING:
                self.logger.debug("处于DOCUMENTING状态，应用特定逻辑。")
                if await self._is_document_confirmation(message):
                    action_command = "finalize_and_reset"
                else:
                    self.logger.info("输入不是确认，视为文档修改请求。")
                    action_command = "execute_document_modification"

            if not action_command:
                self.logger.debug("非DOCUMENTING状态或无特定动作，使用通用意图决策引擎。")
                decision_result = await self.intent_decision_engine.analyze(
                    message,
                    context=[{
                        "session_id": session_id,
                        "current_state": self.current_state.name,
                        "domain": self.current_domain,
                        "category": self.current_category,
                        "history": history
                    }]
                )
                if decision_result.get("error"):
                    raise Exception(decision_result["error"])
                action_command = decision_result.get('decision', {}).get('action')

            self.logger.info(f"当前状态: {self.current_state.name} | 决策指令: '{action_command}'")

            # 尝试使用整合回复系统
            response_text = await self._process_with_integrated_system(
                message, session_id, decision_result, history, action_command
            )

            # 如果整合系统失败，回退到原有逻辑
            if not response_text:
                response_text = await self._process_with_fallback_system(
                    message, session_id, decision_result, history, action_command
                )

            # 记录监控指标
            await self._record_reply_metrics(
                session_id=session_id,
                reply_key=action_command or "unknown",
                content=response_text,
                response_time=time.time() - start_time,
                success=bool(response_text),
                decision_result=decision_result
            )

            if response_text:
                await self.message_manager.save_message(
                    conversation_id=session_id, sender_type="ai", content=response_text
                )

            return await self._build_response(response_text, session_id)

        except Exception as e:
            self.logger.error(f"处理消息失败: {str(e)}", exc_info=True)

            # 记录失败指标
            await self._record_reply_metrics(
                session_id=session_id,
                reply_key="error",
                content="",
                response_time=time.time() - start_time,
                success=False,
                error_message=str(e)
            )

            return await self._build_error_response(f"抱歉，处理您的请求时发生意外错误。", session_id)

    # 整合系统处理方法已移至 ConversationFlowReplyMixin

    # 统一回复方法和监控记录方法已移至 ConversationFlowReplyMixin

    # 尝试从数据库恢复会话状态，确保状态与实际情况一致
    async def try_restore_session_state(self, session_id: str):
        
        # 仅当内存中没有领域信息时才加载，避免重复加载
        if not self.current_domain and session_id:
            try:
                # 首先，尝试恢复领域/类别信息，判断流程是否已开始
                restored = await self._restore_domain_category_info(session_id)

                if restored:
                    # 如果领域/类别已存在，检查是否有文档或关注点，以确定具体状态
                    existing_docs = await self.db_manager.get_documents_by_conversation_id(session_id)
                    if existing_docs:
                        self.logger.info(f"发现会话 {session_id} 已有文档，转换到DOCUMENTING状态。")
                        self.transition_to(ConversationState.DOCUMENTING)
                    else:
                        # 无论是否有关注点，只要确定了领域和类别，就进入信息收集阶段
                        self.logger.info(f"会话 {session_id} 已恢复领域/类别，转换到COLLECTING_INFO状态。")
                        self.transition_to(ConversationState.COLLECTING_INFO)
                        # 在恢复后加载关注点状态到内存，并清理不一致的processing状态
                        await self.status_manager._load_status_to_memory(session_id)
                        await self.status_manager.clear_all_processing_status()
                else:
                    self.logger.debug(f"数据库中没有会话 {session_id} 的活动状态，保持IDLE。")
                    self.transition_to(ConversationState.IDLE)

            except Exception as e:
                self.logger.error(f"尝试恢复会话 {session_id} 状态时出错: {str(e)}")

    # 从数据库恢复领域和类别信息（使用 domain_id 和 category_id）
    async def _restore_domain_category_info(self, session_id: str) -> bool:
        """
        如果成功恢复，返回 True，否则返回 False。
        """
        if self.current_domain and self.current_category:
            return True  # 状态已在内存中

        self.logger.debug(f"尝试从数据库恢复会话 {session_id} 的领域/类别信息。")
        try:
            query = "SELECT domain_id, category_id FROM conversations WHERE conversation_id = ?"
            result = await self.db_manager.get_record(query, (session_id,))

            if result and result.get("domain_id") and result.get("category_id"):
                self.current_domain = result["domain_id"]
                self.current_category = result["category_id"]
                self.logger.info(f"从数据库恢复会话状态 - 领域: {self.current_domain}, 类别: {self.current_category}")

                # 同时加载关注点定义和问题陈述
                self.current_focus_points_definitions = await self.load_focus_points(
                    session_id, self.current_domain, self.current_category
                )
                self.problem_statement = await self.message_manager.get_first_user_message(session_id)
                if self.problem_statement:
                    self.logger.info(f"从历史消息恢复核心问题陈述: '{self.problem_statement}'")
                return True
            else:
                self.logger.info(f"数据库中未找到会话 {session_id} 的领域/类别信息。")
                return False
        except Exception as e:
            self.logger.warning(f"无法从数据库恢复领域/类别信息，可能是表结构问题: {str(e)}")
            return False
    
    # ==================== Handler方法 - 处理特定的用户操作 ====================

    async def handle_reset_conversation(self, message: str = "", session_id: str = "", decision_result: Dict[str, Any] = None, **kwargs) -> str:
        """
        处理重置会话的指令

        当用户请求重置当前会话时，清除所有会话状态和数据，
        将系统恢复到初始状态，准备开始新的对话。

        参数:
            message (str): 用户消息（此handler中未使用）
            session_id (str): 会话标识符
            decision_result (Dict[str, Any]): 决策结果（此handler中未使用）
            **kwargs: 其他参数

        返回:
            str: 重置确认消息

        操作:
            - 重置会话状态为IDLE
            - 清除关注点状态
            - 清除领域和分类信息
            - 重置所有相关的会话数据
        """
        self.logger.info(f"用户 {session_id} 请求重置会话")
        await self.reset_state(session_id)
        return await self._get_reset_confirmation_message()
    
    # 根据用户响应生成下一个问题
    async def generate_next_question(self, session_id: str, user_message: str, focus_points: List[Dict[str, Any]]) -> str:
        try:
            if not focus_points:
                self.logger.error("generate_next_question: 关注点列表为空")
                return self._get_default_requirement_prompt()

            self.logger.debug("寻找下一个待处理的关注点...")
            next_point_to_ask = await self.status_manager.get_next_pending_point(focus_points)

            if next_point_to_ask:
                point_id = next_point_to_ask["id"]
                self.logger.info(f"找到下一个待处理的关注点: {point_id} - {next_point_to_ask['name']}")

                # 使用安全的方法设置processing状态
                await self.status_manager.set_point_processing(session_id, point_id)

                base_question = f"关于'{next_point_to_ask['name']}'，{next_point_to_ask['description']}"
                if 'example' in next_point_to_ask and next_point_to_ask['example']:
                    base_question += f" {next_point_to_ask['example']}"
                
                question = base_question
                
                if self.llm_client:
                    try:
                        user_context = self.problem_statement or "用户的具体需求"
                        self.logger.info(f"问题优化器使用的上下文: '{user_context}'")

                        prompt_loader = PromptLoader()
                        prompt = prompt_loader.load_prompt(
                            "question_polisher",
                            {"user_input": user_context, "base_question": base_question}
                        )
                        response = await self.llm_client.call_llm(
                            messages=[{"role": "user", "content": prompt}],
                            agent_name="question_polisher",
                            temperature=0.7
                        )
                        question = response.get("content", base_question).strip()
                    except Exception as e:
                        self.logger.warning(f"润色问题失败，使用原始问题: {str(e)}")

                return question

            else:
                self.logger.info("所有关注点已完成采集，开始生成文档")
                self.transition_to(ConversationState.DOCUMENTING)
                document_content = await self._generate_document(session_id)
                document_generated_msg = await self._get_document_generated_message()
                return document_generated_msg + "\n\n" + document_content

        except Exception as e:
            self.logger.error(f"生成后续问题失败: {str(e)}", exc_info=True)
            return await self._get_default_requirement_prompt()
    
    # 处理文档修改请求
    async def handle_document_modification(self, message: str = "", session_id: str = "", decision_result: Dict[str, Any] = None, **kwargs) -> str:
        try:
            if not hasattr(self, 'review_and_refine_agent') or self.review_and_refine_agent is None:
                from backend.agents.review_and_refine import ReviewAndRefineAgent
                self.review_and_refine_agent = ReviewAndRefineAgent()

            refine_result = await self.review_and_refine_agent.process_message({
                "text": message,
                "session_id": session_id
            })

            if refine_result.get("success"):
                next_action = refine_result.get("next_action")
                if next_action == "completed":
                    await self.reset_state(session_id)
                    return await self._get_document_finalized_message()
                elif next_action == "review_again":
                    return refine_result.get("text_response", "文档已根据您的要求进行修改，请查看。")

            return await self._get_modification_error_message()

        except Exception as e:
            self.logger.error(f"处理文档修改时出错: {str(e)}")
            return await self._get_system_error_message()
    
    # 处理最终确认并重置会话
    async def handle_finalize_and_reset(self, message: str = "", session_id: str = "", decision_result: Dict[str, Any] = None, **kwargs) -> str:
        await self.reset_state(session_id)
        return await self._get_document_finalized_message()
    
    # 构建响应结构
    async def _build_response(self, text_response: str, session_id: str) -> Dict[str, Any]:
        formatted_text = text_response

        return {
            "text_response": formatted_text,
            "domain_result": self.latest_domain_result,
            "category_result": self.latest_category_result,
            "focus_points_status": await self._get_formatted_focus_points_status(session_id)
        }
    
    # 构建错误响应结构
    async def _build_error_response(self, error_message: str, session_id: str) -> Dict[str, Any]:
        # 禁用文本格式化以保持原始格式
        # formatted_error = text_formatter.format_for_markdown(error_message) if error_message else error_message
        formatted_error = error_message

        return {
            "text_response": formatted_error,
            "error": True,
            "domain_result": self.latest_domain_result,
            "category_result": self.latest_category_result,
            "focus_points_status": await self._get_formatted_focus_points_status(session_id)
        }

    # 加载指定领域和类别的关注点
    async def load_focus_points(self, session_id: str, domain_id: str, category_id: str) -> List[Dict[str, Any]]:
        try:
            if not self.knowledge_base_agent:
                self.logger.error("knowledge_base_agent未初始化")
                return []

            if not category_id:
                self.logger.warning(f"未提供category_id，无法获取关注点 - domain_id: {domain_id}")
                return []

            focus_points = self.knowledge_base_agent.get_concern_points(category_id=category_id)

            if not focus_points:
                self.logger.error(f"未找到domain_id={domain_id}, category_id={category_id}的关注点")
                return []

            await self.status_manager.initialize_focus_points(session_id, focus_points)
            return focus_points

        except Exception as e:
            self.logger.error(f"加载关注点失败: {str(e)}")
            return []

    # 处理意图
    async def _process_intent(self, message: str = "", session_id: str = "", decision_result: Dict[str, Any] = None, **kwargs) -> str:
        try:
            domains = self.knowledge_base_agent.get_domains() if self.knowledge_base_agent else []

            domain_result = await self.domain_classifier_agent.classify(
                message, domains, self.history
            )
            self.latest_domain_result = domain_result

            if domain_result.get("status") == "pending":
                domain_result_with_input = domain_result.copy()
                domain_result_with_input['user_input'] = message

                # 从decision_result中获取prompt_instruction
                prompt_instruction = None
                if decision_result:
                    prompt_instruction = decision_result.get('decision', {}).get('prompt_instruction')

                return await self._generate_domain_guidance_question(domains, domain_result_with_input, prompt_instruction)
            
            self.current_domain = domain_result.get("domain_id")
            
            self.problem_statement = message
            self.logger.info(f"记录核心问题陈述: '{self.problem_statement}'")
            
            self.current_category = domain_result.get("category_id")
            if self.current_domain and not self.current_category and self.category_classifier_agent:
                categories = self.knowledge_base_agent.get_categories(self.current_domain) if self.knowledge_base_agent else []
                if categories:
                    category_result = await self.category_classifier_agent.classify(message, categories, self.history)
                    self.latest_category_result = category_result
                    if category_result and category_result.get("status") == "completed":
                        self.current_category = category_result.get("category_id")
                    else:
                        self.current_category = categories[0].get("category_id")
                        self.latest_category_result = {"category_id": self.current_category, "status": "defaulted"}
                else:
                    self.current_category = None
                    self.latest_category_result = None
            
            if self.current_domain and self.current_category:
                await self.db_manager.execute_update(
                    """
                    UPDATE conversations SET domain_id = ?, category_id = ?
                    WHERE conversation_id = ?
                    """,
                    (self.current_domain, self.current_category, session_id)
                )
                self.logger.info(f"已将领域 '{self.current_domain}' 和类别 '{self.current_category}' 保存到会话 '{session_id}'。")

            focus_points = await self.load_focus_points(
                session_id, self.current_domain, self.current_category
            )
            self.current_focus_points_definitions = focus_points if focus_points else []

            if not self.current_focus_points_definitions:
                return "抱歉，暂时无法处理您的请求。"

            question_result = await self.generate_initial_question(focus_points, session_id, message)
            if question_result and question_result.get("content"):
                suggested_state = question_result.get("suggested_state")
                if suggested_state == "DOCUMENTING":
                    self.transition_to(ConversationState.DOCUMENTING)
                elif suggested_state == "COLLECTING_INFO":
                    self.transition_to(ConversationState.COLLECTING_INFO)
                
                return question_result.get("content")

            return self._get_default_requirement_prompt()

        except Exception as e:
            error_msg = getattr(e, 'message', str(e))
            self.logger.error(f"处理意图失败: {error_msg}")
            return self._get_processing_error_message(error_msg)

    # 根据关注点列表生成第一个采集问题
    async def generate_initial_question(self, focus_points: List[Dict[str, Any]], session_id: str = None, user_input: str = None) -> Dict[str, Any]:
       
        try:
            if not focus_points:
                return {"type": "default", "content": "请描述您的详细需求。", "suggested_state": None}

            if session_id:
                await self.status_manager.initialize_focus_points(session_id, focus_points)
                
                if user_input:
                    # 1. 获取对话开始前的历史摘要
                    historical_summary_json = await self.summary_manager.get_summary(session_id)
                    
                    # 2. 调用信息提取器，获取更新后的摘要和本次变化的关注点
                    extraction_result = await self.information_extractor_agent.extract_values(
                        user_input=user_input,
                        focus_points=focus_points,
                        historical_summary=historical_summary_json
                    )

                    # 3. 更新权威的状态源：conversation_summaries 表
                    new_summary_json = extraction_result.get("updated_summary_json", historical_summary_json)
                    await self.summary_manager.update_summary(session_id, new_summary_json)
                    
                    # --- 关键修复：将新的摘要状态同步到旧的状态管理数据库 ---
                    # 为所有关注点创建一个名称到ID的映射，以便快速查找
                    focus_points_map = {fp["name"]: fp for fp in focus_points}
                    
                    # 解析新的摘要，以获取所有关注点的最新状态
                    summary_data = json.loads(new_summary_json)
                    latest_points_status = summary_data.get("extracted_points", [])

                    # 遍历最新状态，并使用 status_manager 更新 concern_point_coverage 表
                    for point_status in latest_points_status:
                        point_name = point_status.get("name")
                        if point_name in focus_points_map:
                            point_id = focus_points_map[point_name].get("id")
                            completeness = point_status.get("completeness", 0.0)
                            extracted_value = point_status.get("value", "")
                            
                            # 根据完整度决定状态
                            status = "completed" if completeness >= 0.7 else "pending"
                            
                            await self.status_manager.update_focus_point_status(
                                session_id=session_id,
                                point_id=point_id,
                                status=status,
                                value=extracted_value,
                                additional_data={"completeness": completeness}
                            )
                    
                    # 现在 status_manager 已经同步，无需再从数据库加载
                    # await self.status_manager._load_status_to_memory(session_id) # 此行可以移除或保留
                    # --- 修复结束 ---


            all_required_covered = all(
                self.status_manager.get_focus_point_status(p["id"]).get("status") == "completed"
                for p in focus_points if p.get("priority") in ["P0", "P1"]
            )
            
            if all_required_covered:
                document_content = await self._generate_document(session_id)
                return {
                    "type": "document",
                    "content": "根据您提供的信息，我已生成需求文档。请查看并确认：\n\n" + document_content,
                    "suggested_state": "DOCUMENTING"
                }

            selected_point = await self.status_manager.get_next_pending_point(focus_points)

            if selected_point:
                point_id = selected_point["id"]
                base_question = f"关于'{selected_point['name']}'，{selected_point['description']}"
                if 'example' in selected_point and selected_point['example']:
                    base_question += f" {selected_point['example']}"
                
                question = base_question
                
                if self.llm_client:
                    try:
                        user_context = user_input or "用户的具体需求" 
                        prompt_loader = PromptLoader()
                        prompt = prompt_loader.load_prompt(
                            "question_polisher",
                            {"user_input": user_context, "base_question": base_question}
                        )
                        response = await self.llm_client.call_llm(
                            messages=[{"role": "user", "content": prompt}],
                            agent_name="question_polisher",
                            temperature=0.7
                        )
                        question = response.get("content", base_question)
                    except Exception as e:
                        self.logger.warning(f"润色问题失败，使用原始问题: {str(e)}")
              
                if session_id:
                    # 使用安全的方法设置processing状态
                    await self.status_manager.set_point_processing(session_id, point_id)

                return {"type": "question", "content": question, "suggested_state": "COLLECTING_INFO"}

            return {"type": "default", "content": "请您详细描述您的需求...", "suggested_state": None}
        except Exception as e:
            self.logger.error(f"生成初始问题失败: {str(e)}")
            return {"type": "default", "content": "请您详细描述您的需求...", "suggested_state": None}

    # 格式化关注点状态
    async def _get_formatted_focus_points_status(self, session_id: str) -> List[Dict[str, Any]]:

        formatted_status_list = []
        try:
            if not self.current_domain or not self.current_category:
                return []
            
            raw_statuses = await self.status_manager._load_status_to_memory(session_id)

            if not self.current_focus_points_definitions:
                self.current_focus_points_definitions = await self.load_focus_points(session_id, self.current_domain, self.current_category)
                if not self.current_focus_points_definitions:
                    return []

            for point_def in self.current_focus_points_definitions:
                point_id = point_def.get("id")
                if not point_id:
                    continue

                status_info = raw_statuses.get(point_id, {})
                extracted_value = status_info.get("value")
                current_status = status_info.get("status", "pending")
                
                formatted_status_list.append({
                    "point": point_def.get("name", f"未知关注点 ({point_id})"),
                    "value": extracted_value,
                    "status": current_status,
                    "priority": point_def.get("priority", "P2")
                })
            
        except Exception as e:
            self.logger.error(f"[{session_id}] 格式化关注点状态时出错: {str(e)}", exc_info=True)
            return []
            
        return formatted_status_list

    # 状态转换
    def transition_to(self, new_state: ConversationState) -> None:
        self.logger.info(f"状态转换: {self.current_state.name} -> {new_state.name}")
        self.current_state = new_state

    # 重置会话状态，包括内存和数据库中的状态
    async def reset_state(self, session_id: str):
        self.logger.info(f"重置会话 {session_id} 的状态。")
        self.current_state = ConversationState.IDLE
        self.latest_domain_result = None
        self.latest_category_result = None
        self.current_focus_points_definitions = []
        self.current_domain = None
        self.current_category = None
        self.problem_statement = None # 重置核心问题陈述
        
        if self.status_manager:
            await self.status_manager.reset(session_id)
            self.logger.info(f"FocusPointStatusManager 已为会话 {session_id} 重置。")
        
        # 同时清除数据库中存储的领域/类别信息
        try:
            await self.db_manager.execute_update(
                """
                UPDATE conversations
                SET domain_id = NULL, category_id = NULL
                WHERE conversation_id = ?
                """,
                (session_id,)
            )
            self.logger.info(f"已清除数据库中会话 {session_id} 的领域/类别信息。")
        except Exception as e:
            self.logger.error(f"清除数据库中会话 {session_id} 的领域/类别信息时失败: {e}")

        self.logger.info(f"会话 {session_id} 状态已重置为 IDLE。")
        self.transition_to(ConversationState.IDLE)

    # 生成需求确认文档
    async def _generate_document(self, session_id: str) -> str:

        try:
            # ... 此方法保持不变 ...
            domain = self.current_domain
            category_id = self.current_category
            category_name = None
            if self.knowledge_base_agent and category_id:
                categories = self.knowledge_base_agent.get_categories(domain)
                category_obj = next((c for c in categories if c.get('id') == category_id), None)
                if category_obj:
                    category_name = category_obj.get('name')
            
            project_name = f"{category_name}需求文档" if category_name else "需求文档"
            
            if self.document_generator_agent:
                document_id = await self.document_generator_agent.generate_document(
                    conversation_id=session_id,
                    project_name=project_name
                )
                
                if document_id:
                    document_content = await self._get_document_content(document_id)
                    doc_guidance = (
                        "\n\n---\n"
                        "文档操作指引：\n"
                        "1. 输入'确认' - 确认文档无误并完成\n"
                        "2. 指出需要修改的部分 - 例如'修改功能描述部分'\n"
                        "3. 输入'新需求' - 开始新的需求采集\n"
                        "4. 输入'全部重来' - 重新开始整个需求采集流程"
                    )
                    return document_content + doc_guidance
                else:
                    return self._get_document_generation_failed_message()
            else:
                return self._get_document_generator_not_initialized_message()
        except Exception as e:
            self.logger.error(f"生成文档失败: {str(e)}")
            return "生成文档时出错，请稍后重试。"

    # 从数据库获取文档内容
    async def _get_document_content(self, document_id: str) -> str:
        try:
            query = "SELECT content FROM documents WHERE document_id = ?"
            result = await self.db_manager.execute_query(query, (document_id,))
            if result and len(result) > 0:
                return result[0]["content"]
            else:
                return self._get_document_not_found_message()
        except Exception as e:
            return f"获取文档内容时出错: {str(e)}"
    
    # --- 新增的辅助方法 ---
    async def _is_document_confirmation(self, text: str) -> bool:
        """判断是否为文档确认请求"""
        confirmation_keywords = ["确认", "没问题", "正确", "可以", "同意", "是的", "ok", "okay", "确认无误"]
        # 增加否定和修改关键词，避免误判
        negation_keywords = ["不", "不是", "不要", "别", "修改", "调整", "但是", "不过"]
        
        self.logger.debug(f"[_is_document_confirmation] 开始分析用户输入: '{text}'")
        text_lower = text.lower()
        
        has_confirm = any(keyword in text_lower for keyword in confirmation_keywords)
        has_negation_or_modification = any(neg_keyword in text_lower for neg_keyword in negation_keywords)
        
        # 确认意图：有确认词，且没有否定或修改意图的词
        result = has_confirm and not has_negation_or_modification
        self.logger.debug(f"[_is_document_confirmation] 判断结果: {result}")
        return result
    
    # 使用LLM生成个性化的领域引导问题(v2.5)
    async def _generate_domain_guidance_question(self, domains: List[Dict[str, Any]], domain_result: Dict[str, Any],prompt_instruction: Optional[str] = None) -> str:
        try:
            user_input = domain_result.get('user_input', '')
            domain_info = []
            for domain in domains:
                domain_name = domain.get('name', '未知领域')
                domain_desc = domain.get('description', '')
                if domain_desc:
                    domain_info.append(f"- {domain_name}: {domain_desc}")
                else:
                    domain_info.append(f"- {domain_name}")
            
            domains_text = "\n".join(domain_info)

            prompt_loader = PromptLoader()
            prompt = prompt_loader.load_prompt(
                "domain_guidance",  # 新模板的名称
                {
                    "user_input": user_input,
                    "domains_text": domains_text,
                    "prompt_instruction": prompt_instruction if prompt_instruction else "无" # 确保即使没有指令也有默认值
                }
            )

          
            if self.llm_client:
                self.logger.debug(f"使用LLM生成领域引导问题 - user_input: {user_input}")
                
                response = await self.llm_client.call_llm(
                    messages=[{"role": "user", "content": prompt}],
                    agent_name="domain_guidance_generator",
                    temperature=0.7
                )
                
                generated_question = response.get("content", "").strip()
                
                # 清理可能的前缀和后缀
                prefixes_to_remove = [
                    "以下是引导问题：", "引导问题：", "问题：", "我的问题是：",
                    "生成的问题：", "建议问题：", "以下是我生成的问题："
                ]
                for prefix in prefixes_to_remove:
                    if generated_question.startswith(prefix):
                        generated_question = generated_question[len(prefix):].strip()
                
                if generated_question and len(generated_question) > 10 and ("?" in generated_question or "？" in generated_question):
                    self.logger.info(f"LLM成功生成领域引导问题: {generated_question[:50]}...")
                    return generated_question
                else:
                    self.logger.warning(f"LLM生成的问题质量不佳，使用fallback: {generated_question}")
                    return self._get_fallback_guidance_question(domains, user_input)
            else:
                self.logger.warning("LLM客户端未初始化，使用fallback方法")
                return self._get_fallback_guidance_question(domains, user_input)
                
        except Exception as e:
            self.logger.error(f"LLM生成领域引导问题失败: {str(e)}")
            return self._get_fallback_guidance_question(domains, domain_result.get('user_input', ''))
    
    
    def _get_fallback_guidance_question(self, domains: List[Dict[str, Any]], user_input: str = "") -> str:
        # ... 此方法保持不变 ...
        return "请详细描述您的需求。"

    # === 任务路由器处理方法 ===
    
    # 显示共情并澄清
    async def handle_show_empathy_and_clarify(self, message: str, session_id: str, decision_result: Dict[str, Any], **kwargs) -> str:
        self.logger.info(f"执行指令: show_empathy_and_clarify (调用LLM)")
        prompt_instruction = decision_result.get('decision', {}).get('prompt_instruction', '用户表达了负面情绪，请先共情再引导。')
        prompt = f"""...
        用户的输入是: "{message}"
        ...
        - {prompt_instruction}
        ...
        """
        try:
            if not self.llm_client: return "听到这个消息我感到很难过。请问有什么我可以帮助您的吗？"
            response = await self.llm_client.call_llm(messages=[{"role": "user", "content": prompt}], temperature=0.7)
            generated_response = response.get("content", "").strip()
            return generated_response or "听到这个消息我感到很难过，需要聊聊吗？"
        except Exception as e:
            return "听到这个消息我感到很难过。请问有什么我可以帮助您的吗？"

    # 确认并重定向
    async def handle_acknowledge_and_redirect(self, message: str, session_id: str, decision_result: Dict[str, Any], **kwargs) -> str:
        self.logger.info(f"执行指令: acknowledge_and_redirect")
        prompt_instruction = decision_result.get('decision', {}).get('prompt_instruction')
        return prompt_instruction or "好的，谢谢您的分享。让我们回到正题吧，请问有什么业务需求我可以帮您处理？"

    # 提供建议
    async def handle_provide_suggestions(self, message: str = "", session_id: str = "", **kwargs) -> str:
        """
        处理提供建议的请求 (数据驱动版)。
        不再使用硬编码的建议，而是从知识库动态加载。
        """
        self.logger.info("执行指令: provide_suggestions")

        # 1. 找出所有待处理的关注点
        all_statuses = await self.status_manager._load_status_to_memory(session_id)
        pending_points_defs = []
        for point_def in self.current_focus_points_definitions:
            point_id = point_def.get("id")
            if all_statuses.get(point_id, {}).get("status", "pending") == "pending":
                pending_points_defs.append(point_def)

        # 2. 如果只有一个待处理的关注点，为其提供具体建议
        if len(pending_points_defs) == 1:
            point_def = pending_points_defs[0]
            point_name = point_def.get("name", "这个方面")
            
            # 假设 knowledge_base_agent 返回的关注点定义中包含 'suggestions' 字段
            # suggestions 可以是一个字符串或一个字符串列表
            suggestions = point_def.get("suggestions")
            
            if suggestions:
                if isinstance(suggestions, list):
                    # 如果是列表，格式化成有序列表
                    formatted_suggestions = "\n".join(f"{i+1}. {s}" for i, s in enumerate(suggestions))
                    response = f"关于“{point_name}”，我这里有一些建议供您参考：\n\n{formatted_suggestions}\n\n您觉得哪几点比较符合您的想法，或者您有其他补充吗？"
                else:
                    # 如果是单个字符串
                    response = f"关于“{point_name}”，我的一点建议是：\n\n{suggestions}\n\n您对此有什么看法？"
                return response
            else:
                # 如果知识库没有提供具体建议，则使用通用描述作为后备
                self.logger.warning(f"关注点 '{point_name}' 没有配置具体的建议，使用其描述信息作为引导。")
                description = point_def.get('description', '')
                return f"关于“{point_name}”，您可以从以下方面思考：{description}。请告诉我您的具体想法或需求。"

        # 3. 如果有多个待处理的关注点，引导用户选择
        elif len(pending_points_defs) > 1:
            point_names = [p['name'] for p in pending_points_defs]
            return f"我注意到还有几个重要信息需要确认：{', '.join(point_names)}。让我们先从最重要的开始，您希望我针对哪个方面给您一些建议呢？"

        # 4. 如果没有待处理的关注点，则提供与类别相关的通用建议
        else:
            self.logger.info("没有待处理的关注点，提供与类别相关的通用建议。")
            # 假设可以从知识库获取与类别相关的通用建议
            # 注意: 您需要在 KnowledgeBaseAgent 中实现 get_general_suggestions 方法
            general_suggestions = self.knowledge_base_agent.get_general_suggestions(self.current_category)

            if general_suggestions:
                if isinstance(general_suggestions, list):
                    formatted_suggestions = "\n".join(f"- {s}" for s in general_suggestions)
                    return f"基于您目前提供的全部信息，我有以下几点通用建议：\n\n{formatted_suggestions}\n\n您希望我详细解释哪个方面呢？"
                else:
                    return str(general_suggestions)
            else:
                # 最终的后备方案
                return "所有信息看起来都已覆盖。如果您希望，我可以为您生成一份总结文档。或者，您还有其他方面的需求想要讨论吗？"
    
    async def handle_greeting(self, message: str = "", session_id: str = "", decision_result: Dict[str, Any] = None, **kwargs) -> str:
        """
        处理问候指令 - 使用新的动态回复生成器

        当用户发送问候消息时，生成个性化的问候回复。
        优先使用动态回复生成器创建个性化回复，失败时回退到静态回复。

        参数:
            message (str): 用户的问候消息
            session_id (str): 会话标识符
            decision_result (Dict[str, Any]): 决策引擎的分析结果
                - 包含prompt_instruction用于动态回复生成
            **kwargs: 其他参数

        返回:
            str: 生成的问候回复

        处理逻辑:
            1. 检查是否有prompt_instruction用于动态生成
            2. 使用DynamicReplyGenerator生成个性化问候
            3. 失败时回退到标准问候消息

        特点:
            - 支持个性化：基于用户消息生成定制化问候
            - 容错性强：动态生成失败时自动回退
            - 性能监控：所有回复都会被监控系统记录
        """
        try:
            if decision_result:
                prompt_instruction = decision_result.get('decision', {}).get('prompt_instruction')
                if prompt_instruction and self.reply_factory:
                    # 使用动态回复生成器
                    return await self.reply_factory.generate_greeting_reply(
                        prompt_instruction=prompt_instruction,
                        user_message=message,
                        session_id=session_id
                    )

            # 回退到静态回复
            return await self._get_greeting_message()

        except Exception as e:
            self.logger.error(f"处理问候失败: {e}", exc_info=True)
            return await self._get_greeting_message()
    
    # 处理重新表述和询问指令-使用LLM生成澄清问题
    async def handle_rephrase_and_inquire(self, message: str = "", session_id: str = "", decision_result: Dict[str, Any] = None, **kwargs) -> str:
        
        self.logger.info(f"处理重新表述和询问指令 - session_id: {session_id}, 用户输入: '{message}'")

        if decision_result:
            prompt_instruction = decision_result.get('decision', {}).get('prompt_instruction')
            if prompt_instruction:
                # 使用prompt_instruction作为提示词调用LLM生成实际回复
                try:
                    if not self.llm_client:
                        self.logger.error("LLM client未初始化，使用默认澄清消息。")
                        return self._get_clarification_request_message()

                    # 构建包含用户输入的完整提示
                    full_prompt = f"{prompt_instruction}\n\n用户输入: {message}"

                    response = await self.llm_client.call_llm(
                        messages=[{"role": "user", "content": full_prompt}],
                        agent_name="clarification_generator",
                        temperature=0.7
                    )

                    clarification_response = response.get("content", "").strip()
                    if clarification_response:
                        self.logger.info(f"LLM成功生成澄清回复: {clarification_response[:50]}...")
                        return clarification_response
                    else:
                        self.logger.warning("LLM未能生成有效的澄清回复，使用默认消息。")
                        return self._get_clarification_request_message()

                except Exception as e:
                    self.logger.error(f"生成澄清回复时出错: {e}", exc_info=True)
                    return self._get_clarification_request_message()

        return self._get_clarification_request_message()
    
    # 处理道歉和请求改进指令-使用新的动态回复生成器
    async def handle_apology_and_request(self, message: str = "", session_id: str = "", decision_result: Dict[str, Any] = None, **kwargs) -> str:
        """处理道歉和请求 - 使用新的动态回复生成器"""
        try:
            if decision_result:
                prompt_instruction = decision_result.get('decision', {}).get('prompt_instruction')
                if prompt_instruction and self.reply_factory:
                    # 使用动态回复生成器
                    return await self.reply_factory.generate_apology_reply(
                        prompt_instruction=prompt_instruction,
                        user_message=message,
                        session_id=session_id
                    )

            # 回退到静态回复
            return await self._get_document_refinement_message()

        except Exception as e:
            self.logger.error(f"处理道歉请求失败: {e}", exc_info=True)
            return await self._get_document_refinement_message()
    
    # 处理未知情况的兜底方法
    async def handle_unknown_situation(self, message: str, session_id: str, decision_result: Dict[str, Any], **kwargs) -> str:
        self.logger.warning(f"进入未知情况处理流程 - session_id: {session_id}, 用户输入: '{message}'")

        intent_info_raw = decision_result.get('intent')
        intent_string = None # 用于存放最终解析出的意图字符串

        if isinstance(intent_info_raw, dict):
            # 理想情况: 它已经是一个字典
            intent_string = intent_info_raw.get('intent')
        elif isinstance(intent_info_raw, str):
            # 其次: 它是一个字符串，需要判断是JSON还是纯文本
            try:
                # 尝试按JSON格式解析
                intent_dict = json.loads(intent_info_raw)
                intent_string = intent_dict.get('intent')
            except json.JSONDecodeError:
                # 如果解析失败，说明它是一个纯文本字符串，直接使用
                self.logger.warning(f"intent_info是一个纯文本字符串而不是JSON: {intent_info_raw}")
                intent_string = intent_info_raw

        # 现在可以安全地使用解析出的 intent_string 进行判断
        if self.current_state == ConversationState.IDLE and intent_string == 'modify':
            self.logger.info("特殊情况处理：在IDLE状态下收到'modify'意图，将引导用户说明修改内容。")
            return "没关系，请问您想修改哪部分内容呢？"

        prompt_instruction = decision_result.get('decision', {}).get('prompt_instruction')

        if not prompt_instruction:
            prompt_instruction = "抱歉，我暂时无法理解您的意思，可以换一种方式告诉我吗？"

        # 使用prompt_instruction作为提示词调用LLM生成实际回复
        try:
            if not self.llm_client:
                self.logger.error("LLM client未初始化，使用默认未知情况消息。")
                return prompt_instruction

            # 构建包含用户输入和意图信息的完整提示
            full_prompt = f"{prompt_instruction}\n\n用户输入: {message}\n意图信息: {intent_string or '未知'}"

            response = await self.llm_client.call_llm(
                messages=[{"role": "user", "content": full_prompt}],
                agent_name="unknown_situation_handler",
                temperature=0.7
            )

            unknown_response = response.get("content", "").strip()
            if unknown_response:
                self.logger.info(f"LLM成功生成未知情况回复: {unknown_response[:50]}...")
                return unknown_response
            else:
                self.logger.warning("LLM未能生成有效的未知情况回复，使用默认消息。")
                return prompt_instruction

        except Exception as e:
            self.logger.error(f"生成未知情况回复时出错: {e}", exc_info=True)
            return prompt_instruction

    # 处理跳过当前问题并询问下一个问题
    async def handle_skip_question_and_ask_next(self, message: str = "", session_id: str = "", decision_result: Dict[str, Any] = None, **kwargs) -> str:
        self.logger.info("正在处理跳过问题的请求...")
        
        # 标记当前关注点为跳过
        processing_point_id = await self.status_manager.get_processing_point()
        if processing_point_id:
            await self.status_manager.update_focus_point_status(
                session_id, processing_point_id, "skipped", "用户选择跳过"
            )
            self.logger.info(f"关注点 {processing_point_id} 已标记为跳过")
        else:
            self.logger.warning("请求跳过问题，但没有找到正在处理的关注点。")
        
        # 立即生成下一个问题
        next_question = await self.generate_next_question(session_id, message, self.current_focus_points_definitions)
        return next_question or "好的，我们继续下一个问题。"
    
    # 请求用户澄清-使用LLM生成澄清请求
    async def handle_request_clarification(self, message: str, session_id: str, decision_result: Dict[str, Any], **kwargs) -> str:
        self.logger.info(f"请求用户澄清 - session_id: {session_id}, 用户输入: '{message}'")

        prompt_instruction = decision_result.get('decision', {}).get('prompt_instruction')
        if prompt_instruction:
            # 使用prompt_instruction作为提示词调用LLM生成实际回复
            try:
                if not self.llm_client:
                    self.logger.error("LLM client未初始化，使用默认澄清消息。")
                    return "抱歉，我没有完全理解您的意思，能否请您用其他方式重新描述一下？"

                # 构建包含用户输入的完整提示
                full_prompt = f"{prompt_instruction}\n\n用户输入: {message}"

                response = await self.llm_client.call_llm(
                    messages=[{"role": "user", "content": full_prompt}],
                    agent_name="clarification_request_generator",
                    temperature=0.7
                )

                clarification_request = response.get("content", "").strip()
                if clarification_request:
                    self.logger.info(f"LLM成功生成澄清请求: {clarification_request[:50]}...")
                    return clarification_request
                else:
                    self.logger.warning("LLM未能生成有效的澄清请求，使用默认消息。")
                    return "抱歉，我没有完全理解您的意思，能否请您用其他方式重新描述一下？"

            except Exception as e:
                self.logger.error(f"生成澄清请求时出错: {e}", exc_info=True)
                return "抱歉，我没有完全理解您的意思，能否请您用其他方式重新描述一下？"

        return "抱歉，我没有完全理解您的意思，能否请您用其他方式重新描述一下？"
    
    # 处理未知的action指令
    async def handle_unknown_action(self, message: str = "", session_id: str = "", decision_result: Dict[str, Any] = None, **kwargs) -> str:
        self.logger.warning(f"收到了一个未知的action指令")
        return await self._get_unknown_action_message()
    
    # 处理用户回答并根据信息完整度决定是“追问”还是“问下一个”
    async def handle_process_answer_and_ask_next(self, message: str = "", session_id: str = "", decision_result: Dict[str, Any] = None, history: List[Dict] = None, **kwargs) -> str:
        self.logger.info("正在处理用户回答...")
        if not self.current_focus_points_definitions:
            await self._restore_domain_category_info(session_id)

        # 1. 确定当前正在处理的关注点
        processing_point_id = await self.status_manager.get_processing_point()
        if not processing_point_id:
            self.logger.warning("请求处理回答，但没有找到正在处理的关注点。将直接寻找下一个待处理问题。")
            # 在没有找到processing关注点时，清理可能存在的不一致状态
            await self.status_manager.clear_all_processing_status()
            return await self.generate_next_question(session_id, message, self.current_focus_points_definitions)

        point_definition = next((p for p in self.current_focus_points_definitions if p.get("id") == processing_point_id), None)
        if not point_definition:
            self.logger.error(f"无法找到ID为 {processing_point_id} 的关注点定义，跳过该问题。")
            return await self.generate_next_question(session_id, message, self.current_focus_points_definitions)

        # --- 2. 统一的数据处理流程（只调用一次提取器） ---
        # 获取历史摘要
        historical_summary_json = await self.summary_manager.get_summary(session_id)

        # 调用提取器，传入历史摘要并获取更新后的结果
        # 注意: 这里只针对当前问题点进行提取，以提高效率和准确性
        extraction_result = await self.information_extractor_agent.extract_values(
            user_input=message,
            focus_points=[point_definition],
            historical_summary=historical_summary_json
        )

        # 更新权威的状态源：conversation_summaries 表
        new_summary_json = extraction_result.get("updated_summary_json", historical_summary_json)
        await self.summary_manager.update_summary(session_id, new_summary_json)

        # --- 3. 解析结果并同步状态 ---
        summary_data = json.loads(new_summary_json)
        latest_points_status = summary_data.get("extracted_points", [])
        
        # 找到本次更新的关注点信息
        updated_point_info = next((p for p in latest_points_status if p.get("name") == point_definition.get("name")), None)

        # --- 4. 决策：追问还是问下一个 ---
        if updated_point_info:
            completeness = updated_point_info.get("completeness", 0.0)
            extracted_value = updated_point_info.get("value", "")
            
            # 无论完整度如何，都先用最新信息更新 concern_point_coverage 表
            status = "completed" if completeness >= 0.7 else "pending"
            await self.status_manager.update_focus_point_status(
                session_id=session_id,
                point_id=processing_point_id,
                status=status,
                value=extracted_value,
                additional_data={"completeness": completeness}
            )

            if status == "completed":
                # 如果信息完整，就问下一个问题
                self.logger.info(f"关注点 '{point_definition['name']}' 已完成，询问下一个问题。")
                return await self.generate_next_question(session_id, message, self.current_focus_points_definitions)
            else:
                # 如果信息不完整，就生成追问
                self.logger.info(f"关注点 '{point_definition['name']}' 信息不完整 (完整度: {completeness})，生成追问。")
                return await self._generate_clarification_question(point_definition, message, history)
        else:
            # 如果LLM未能提取任何信息，也生成追问
            self.logger.warning(f"未能从用户回答 '{message}' 中提取到关于 '{point_definition.get('name')}' 的任何信息。")
            return await self._generate_clarification_question(point_definition, message, history)

    # 使用LLM为不完整的信息生成一个有针对性的追问
    async def _generate_clarification_question(self, focus_point_def: Dict[str, Any], user_answer: str, history: List[Dict] = None) -> str:
        
        self.logger.info(f"为关注点 '{focus_point_def.get('name')}' 生成追问，基于用户回答: '{user_answer}'")
        try:
            # 格式化历史记录为字符串
            history_str = ""
            if history:
                for turn in history[-5:]: # 只取最近5轮对话，避免过长
                    role = turn.get("role", "user")
                    content = turn.get("content", "").strip()
                    history_str += f"{role}: {content}\n"
            else:
                history_str = "无"

            prompt_loader = PromptLoader()
            prompt = prompt_loader.load_prompt(
                "clarification_question", # 使用我们新建的模板
                {
                    "focus_point_name": focus_point_def.get("name", "当前话题"),
                    "focus_point_description": focus_point_def.get("description", ""),
                    "conversation_history": history_str, # 传递历史记录
                    "user_answer": user_answer
                }
            )

            if not self.llm_client:
                self.logger.error("LLM client未初始化，无法生成追问。")
                return f"关于“{focus_point_def.get('name')}”，您能提供更详细一些的信息吗？"

            response = await self.llm_client.call_llm(
                messages=[{"role": "user", "content": prompt}],
                agent_name="clarification_generator",
                temperature=0.7
            )
            
            clarification_question = response.get("content", "").strip()
            if clarification_question:
                self.logger.info(f"LLM成功生成追问: {clarification_question[:50]}...")
                return clarification_question
            else:
                self.logger.warning("LLM未能生成有效的追问，使用后备方案。")
                return f"关于“{focus_point_def.get('name')}”，您能提供更详细一些的信息吗？"
        except Exception as e:
            self.logger.error(f"生成追问时出错: {e}", exc_info=True)
            return f"关于“{focus_point_def.get('name')}”，您能提供更详细一些的信息吗？"


    # 所有回复消息方法已移至 ConversationFlowMessageMixin
