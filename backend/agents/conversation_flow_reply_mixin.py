#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ConversationFlow回复系统混入类
将回复相关功能从主类中分离出来，保持代码整洁

本模块提供了ConversationFlow的回复系统功能，包括：
- 新回复系统组件的初始化和管理
- 整合决策引擎的处理逻辑
- 统一的回复获取和管理
- 回复质量监控和分析
- 回退机制和错误处理

通过混入类设计模式，将回复相关的复杂逻辑从主类中分离，
提高代码的可维护性和可扩展性。
"""

import time
from typing import Dict, Any, List
from datetime import datetime
import logging


class ConversationFlowReplyMixin:
    """
    ConversationFlow回复系统混入类

    这个混入类为ConversationFlow提供了完整的回复系统功能，包括：

    核心功能：
    - 新回复系统组件的初始化（MessageReplyManager、DynamicReplyGenerator等）
    - 整合决策引擎的消息处理流程
    - 统一的回复获取接口，替换原有的18个静态方法
    - 回复质量监控和性能分析
    - 智能回退机制，确保系统稳定性

    设计特点：
    - 模块化设计：将回复功能从主类中分离
    - 容错性强：提供完整的回退机制
    - 监控完善：记录所有回复指标和质量分数
    - 异步支持：支持异步组件初始化和处理

    使用方式：
    通过多重继承的方式混入到ConversationFlow主类中：
    ```python
    class AutoGenConversationFlowAgent(AutoGenBaseAgent, ConversationFlowReplyMixin):
        pass
    ```

    依赖组件：
    - MessageReplyManager: 统一回复管理器
    - DynamicReplyGenerator: 动态回复生成器
    - IntegratedReplySystem: 整合决策引擎
    - TemplateVersionManager: 模板版本管理器
    - ReplyMonitoringSystem: 监控分析系统

    注意事项：
    - 所有组件都有回退机制，初始化失败不会影响系统运行
    - 监控系统会记录所有回复的性能指标
    - 支持热插拔，组件可以独立启用/禁用
    """
    
    def _initialize_reply_systems(self):
        """
        初始化回复系统组件

        按顺序初始化5个核心回复系统组件：
        1. MessageReplyManager - 统一回复管理器
        2. DynamicReplyGenerator - 动态回复生成器
        3. IntegratedReplySystem - 整合决策引擎
        4. TemplateVersionManager - 模板版本管理器
        5. ReplyMonitoringSystem - 监控分析系统

        特点：
        - 容错设计：单个组件初始化失败不影响其他组件
        - 日志记录：详细记录每个组件的初始化状态
        - 回退机制：失败时设置为None，使用回退逻辑

        注意：
        - 此方法在ConversationFlow的__init__中调用
        - 所有组件都是可选的，系统可以在部分组件缺失时正常运行
        """
        try:
            # 1. 消息回复管理器
            try:
                from backend.agents.message_reply_manager import MessageReplyManager
                self.reply_manager = MessageReplyManager(
                    llm_client=self.llm_client,
                    config_path="backend/config/message_reply_config.json"
                )
                self.logger.info("MessageReplyManager 初始化成功")
            except Exception as e:
                self.logger.warning(f"MessageReplyManager 初始化失败: {e}")
                self.reply_manager = None
            
            # 2. 动态回复生成器
            try:
                from backend.agents.dynamic_reply_generator import DynamicReplyGenerator
                self.reply_factory = DynamicReplyGenerator(llm_client=self.llm_client)
                self.logger.info("DynamicReplyGenerator 初始化成功")
            except Exception as e:
                self.logger.warning(f"DynamicReplyGenerator 初始化失败: {e}")
                self.reply_factory = None
            
            # 3. 整合决策引擎
            try:
                from backend.agents.integrated_reply_system import IntegratedReplySystem
                self.integrated_reply_system = IntegratedReplySystem(llm_client=self.llm_client)
                self.logger.info("IntegratedReplySystem 初始化成功")
            except Exception as e:
                self.logger.warning(f"IntegratedReplySystem 初始化失败: {e}")
                self.integrated_reply_system = None
            
            # 4. 模板版本管理器
            try:
                from backend.agents.template_version_manager import TemplateVersionManager
                self.version_manager = TemplateVersionManager()
                self.logger.info("TemplateVersionManager 初始化成功")
            except Exception as e:
                self.logger.warning(f"TemplateVersionManager 初始化失败: {e}")
                self.version_manager = None
            
            # 5. 监控系统
            try:
                from backend.agents.reply_monitoring_system import ReplyMonitoringSystem
                self.monitoring_system = ReplyMonitoringSystem()
                self.logger.info("ReplyMonitoringSystem 初始化成功")
            except Exception as e:
                self.logger.warning(f"ReplyMonitoringSystem 初始化失败: {e}")
                self.monitoring_system = None
            
            self.logger.info("新回复系统组件初始化完成")
            
        except Exception as e:
            self.logger.error(f"初始化回复系统组件失败: {e}", exc_info=True)
            # 设置为None，使用回退机制
            self.reply_manager = None
            self.reply_factory = None
            self.integrated_reply_system = None
            self.version_manager = None
            self.monitoring_system = None

    async def initialize_async_components(self):
        """
        异步初始化组件

        初始化需要数据库连接的异步组件：
        - TemplateVersionManager: 初始化模板版本管理数据库表
        - ReplyMonitoringSystem: 初始化监控系统数据库表

        调用时机：
        - 在ConversationFlow完全初始化后调用
        - 通常在应用启动时的异步初始化阶段

        错误处理：
        - 捕获所有异常，不会因为数据库问题导致系统崩溃
        - 详细记录初始化过程和错误信息

        返回：
        - 无返回值，通过日志记录初始化状态
        """
        try:
            if self.version_manager:
                await self.version_manager.initialize_database()
                self.logger.info("TemplateVersionManager 数据库初始化完成")
            
            if self.monitoring_system:
                await self.monitoring_system.initialize_database()
                self.logger.info("ReplyMonitoringSystem 数据库初始化完成")
                
            self.logger.info("异步组件初始化完成")
            
        except Exception as e:
            self.logger.error(f"异步组件初始化失败: {e}", exc_info=True)

    async def _process_with_integrated_system(
        self, 
        message: str, 
        session_id: str, 
        decision_result: Dict[str, Any], 
        history: List, 
        action_command: str
    ) -> str:
        """使用整合回复系统处理消息"""
        try:
            if not self.integrated_reply_system:
                return None
            
            from backend.agents.integrated_reply_system import DecisionContext
            
            context = DecisionContext(
                intent=decision_result.get("intent"),
                emotion=decision_result.get("emotion"),
                current_state=self.current_state.name,
                session_id=session_id,
                user_message=message,
                conversation_history=history,
                additional_context={
                    "current_domain": self.current_domain,
                    "current_category": self.current_category,
                    "problem_statement": self.problem_statement,
                    "action_command": action_command
                }
            )
            
            reply_result = await self.integrated_reply_system.process_decision_to_reply(
                context=context,
                conversation_flow_instance=self
            )
            
            if reply_result.success:
                self.logger.info(f"整合回复系统处理成功: {action_command}")
                return reply_result.content
            else:
                self.logger.warning(f"整合回复系统处理失败: {reply_result.content}")
                return None
                
        except Exception as e:
            self.logger.error(f"整合回复系统处理异常: {e}", exc_info=True)
            return None

    async def _process_with_fallback_system(
        self, 
        message: str, 
        session_id: str, 
        decision_result: Dict[str, Any], 
        history: List, 
        action_command: str
    ) -> str:
        """回退到原有的消息处理逻辑"""
        try:
            self.logger.info(f"使用回退逻辑处理action: {action_command}")
            
            handler = self.action_router.get(action_command, self.handle_unknown_action)
            response_text = await handler(
                message=message, 
                session_id=session_id, 
                decision_result=decision_result, 
                history=history
            )
            
            return response_text
            
        except Exception as e:
            self.logger.error(f"回退系统处理失败: {e}", exc_info=True)
            return await self._get_reply("system_error")

    async def _get_reply(self, reply_key: str, context: Dict[str, Any] = None, **kwargs) -> str:
        """
        统一的回复获取方法 - 替换所有_get_*_message方法

        这是新回复系统的核心接口，统一管理所有类型的回复获取。
        替换了原有的18个独立的_get_*_message方法。

        参数：
        - reply_key: 回复键，标识要获取的回复类型
        - context: 上下文信息，用于动态回复生成
        - **kwargs: 额外参数，如session_id等

        处理流程：
        1. 优先使用MessageReplyManager获取回复
        2. 如果组件不可用，回退到硬编码回复
        3. 记录错误并提供默认回复

        支持的回复类型：
        - 静态回复：预定义的固定文本
        - 动态回复：基于上下文生成的个性化回复
        - 模板回复：支持变量替换的模板回复

        返回：
        - str: 生成的回复文本

        异常处理：
        - 捕获所有异常，确保总是返回有效回复
        - 失败时使用回退机制提供默认回复
        """
        try:
            if self.reply_manager:
                # 使用新的回复管理器
                return await self.reply_manager.get_reply(
                    reply_key=reply_key,
                    context=context or {},
                    session_id=kwargs.get("session_id"),
                    **kwargs
                )
            else:
                # 回退到硬编码回复
                return self._get_fallback_reply(reply_key, context)
                
        except Exception as e:
            self.logger.error(f"获取回复失败: {e}", exc_info=True)
            return self._get_fallback_reply(reply_key, context)

    def _get_fallback_reply(self, reply_key: str, context: Dict[str, Any] = None) -> str:
        """回退回复映射 - 保持向后兼容"""
        fallback_replies = {
            "reset_confirmation": "好的，我已重置会话。请告诉我您的新需求。",
            "document_finalized": "感谢您的确认！需求文档已最终确定。如果您有新的需求，请随时告诉我。",
            "modification_error": "抱歉，修改文档时出现错误，请稍后再试。",
            "system_error": "系统处理您的请求时遇到问题，请稍后再试。",
            "default_requirement_prompt": "请描述您的详细需求。",
            "document_generated": "所有必要信息已收集完毕，我已根据您提供的信息生成了需求文档。请查看并确认：",
            "initial_guidance": "请您先告诉我您想做什么，例如\"我想开发一个软件\"或\"我需要设计一个Logo\"。",
            "processing_error": f"处理您的请求时出错: {context.get('error_msg', '未知错误') if context else '未知错误'}",
            "unknown_action": "抱歉，我好像遇到了一点内部问题，我们换个话题继续吧？",
            "greeting": "您好！很高兴为您服务。请问有什么可以帮您？",
            "clarification_request": "抱歉我的问题可能不够清晰。让我换一种方式问：请您详细描述一下您的具体需求，这样我能更好地为您提供帮助。",
            "document_refinement": "非常抱歉文档未能让您满意。为了能更正错误，您能具体指出需要修改的部分吗？",
            "domain_info_error": "抱歉，暂时无法获取领域信息。请稍后再试。",
            "specific_requirement_help": "请描述您的具体需求，我将为您提供帮助。",
            "domain_info_fetch_error": "获取领域信息时出错，请稍后再试。",
            "document_generation_failed": "抱歉，文档生成失败，请稍后再试。您可以选择：\n1. 重新尝试生成文档\n2. 修改需求后重试",
            "document_generator_not_initialized": "抱歉，文档生成器未初始化，无法生成文档。请联系管理员解决此问题。",
            "document_not_found": "抱歉，未能找到生成的文档。"
        }
        
        return fallback_replies.get(reply_key, "抱歉，我遇到了一些问题。")

    async def _record_reply_metrics(
        self,
        session_id: str,
        reply_key: str,
        content: str,
        response_time: float,
        success: bool,
        decision_result: Dict[str, Any] = None,
        error_message: str = None
    ):
        """记录回复指标"""
        try:
            if not self.monitoring_system:
                return
            
            from backend.agents.reply_monitoring_system import ReplyMetric
            
            # 确定回复类型
            reply_type = "static"
            if decision_result and decision_result.get('decision', {}).get('prompt_instruction'):
                reply_type = "dynamic"
            
            # 计算质量分数
            quality_score = self._calculate_quality_score(content, success)
            
            metric = ReplyMetric(
                session_id=session_id,
                user_id=session_id,  # 使用session_id作为user_id
                reply_key=reply_key,
                reply_type=reply_type,
                content=content,
                response_time=response_time,
                success=success,
                satisfaction_score=None,  # 需要用户反馈
                quality_score=quality_score,
                timestamp=datetime.now(),
                context={
                    "current_state": self.current_state.name,
                    "current_domain": self.current_domain,
                    "current_category": self.current_category,
                    "decision_result": decision_result
                },
                error_message=error_message,
                fallback_used=not success,
                llm_model=getattr(self.llm_client, 'model_name', None) if hasattr(self, 'llm_client') else None
            )
            
            await self.monitoring_system.record_reply_metric(metric)
            
        except Exception as e:
            self.logger.error(f"记录回复指标失败: {e}", exc_info=True)

    def _calculate_quality_score(self, content: str, success: bool) -> float:
        """计算回复质量分数"""
        if not success or not content:
            return 0.0
        
        score = 0.5  # 基础分数
        
        # 长度评估
        if 10 <= len(content) <= 500:
            score += 0.2
        
        # 内容质量评估
        if "抱歉" not in content and "错误" not in content:
            score += 0.2
        
        # 个性化评估
        if any(word in content for word in ["您", "您的", "为您"]):
            score += 0.1
        
        return min(score, 1.0)
